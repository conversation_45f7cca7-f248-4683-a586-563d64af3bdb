import React, { useState } from 'react';
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  Alert,
  Platform,
} from 'react-native';
import { Feather } from '@expo/vector-icons';
import DateTimePicker from '@react-native-community/datetimepicker';
import { useColorScheme } from 'nativewind';
import Animated, { FadeIn, SlideInDown } from 'react-native-reanimated';

import Input from '../ui/Input';
import Button from '../ui/Button';

interface AddDateModalProps {
  isVisible: boolean;
  onClose: () => void;
  onSave: (dateData: {
    name: string;
    type: string;
    date: Date;
  }) => Promise<void>;
}

const AddDateModal: React.FC<AddDateModalProps> = ({
  isVisible,
  onClose,
  onSave,
}) => {
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  const [name, setName] = useState('');
  const [type, setType] = useState('');
  const [date, setDate] = useState(new Date());
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [saving, setSaving] = useState(false);

  const handleSave = async () => {
    if (!name.trim()) {
      Alert.alert('Error', 'Please enter a name for the date.');
      return;
    }

    try {
      setSaving(true);
      await onSave({
        name: name.trim(),
        type: type.trim() || 'Custom',
        date,
      });
      
      // Reset form
      setName('');
      setType('');
      setDate(new Date());
      onClose();
    } catch (error) {
      Alert.alert('Error', 'Failed to save date. Please try again.');
    } finally {
      setSaving(false);
    }
  };

  const handleClose = () => {
    setName('');
    setType('');
    setDate(new Date());
    onClose();
  };

  const onDateChange = (event: any, selectedDate?: Date) => {
    setShowDatePicker(Platform.OS === 'ios');
    if (selectedDate) {
      setDate(selectedDate);
    }
  };

  return (
    <Modal
      visible={isVisible}
      transparent
      animationType="fade"
      onRequestClose={handleClose}
    >
      <View className="flex-1 bg-black/50 justify-center items-center p-4">
        <Animated.View
          entering={SlideInDown.duration(300)}
          className="w-full max-w-sm bg-card dark:bg-card-dark rounded-xl shadow-lg"
        >
          {/* Header */}
          <View className="flex-row items-center justify-between p-4 border-b border-border dark:border-border-dark">
            <Text className="text-lg font-semibold text-text-primary dark:text-text-primary-dark">
              Add Important Date
            </Text>
            <TouchableOpacity
              onPress={handleClose}
              className="p-2 rounded-full active:bg-gray-100 dark:active:bg-gray-800"
            >
              <Feather name="x" size={20} color={isDark ? '#FFFFFF' : '#000000'} />
            </TouchableOpacity>
          </View>

          {/* Content */}
          <View className="p-4 space-y-4">
            <View>
              <Text className="text-sm font-medium text-text-primary dark:text-text-primary-dark mb-2">
                Date Name *
              </Text>
              <Input
                value={name}
                onChangeText={setName}
                placeholder="e.g., First Date, Graduation"
                className="mb-4"
              />
            </View>

            <View>
              <Text className="text-sm font-medium text-text-primary dark:text-text-primary-dark mb-2">
                Type (Optional)
              </Text>
              <Input
                value={type}
                onChangeText={setType}
                placeholder="e.g., Anniversary, Achievement"
                className="mb-4"
              />
            </View>

            <View>
              <Text className="text-sm font-medium text-text-primary dark:text-text-primary-dark mb-2">
                Date
              </Text>
              <TouchableOpacity
                onPress={() => setShowDatePicker(true)}
                className="flex-row items-center justify-between p-3 border border-border dark:border-border-dark rounded-lg bg-background dark:bg-background-dark"
              >
                <Text className="text-text-primary dark:text-text-primary-dark">
                  {date.toLocaleDateString()}
                </Text>
                <Feather name="calendar" size={20} color={isDark ? '#FFFFFF' : '#000000'} />
              </TouchableOpacity>
            </View>

            {showDatePicker && (
              <DateTimePicker
                value={date}
                mode="date"
                display="default"
                onChange={onDateChange}
              />
            )}
          </View>

          {/* Footer */}
          <View className="flex-row space-x-3 p-4 border-t border-border dark:border-border-dark">
            <Button
              title="Cancel"
              onPress={handleClose}
              variant="secondary"
              className="flex-1"
              disabled={saving}
            />
            <Button
              title="Save"
              onPress={handleSave}
              variant="primary"
              className="flex-1"
              isLoading={saving}
              disabled={saving}
            />
          </View>
        </Animated.View>
      </View>
    </Modal>
  );
};

export default AddDateModal;
