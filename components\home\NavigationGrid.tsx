import React from 'react';
import { View, Text, TouchableOpacity, Dimensions } from 'react-native';
import { Feather, MaterialCommunityIcons } from '@expo/vector-icons';
import Animated, { FadeIn, SlideInUp } from 'react-native-reanimated';
import { useRouter } from 'expo-router';
import { useColorScheme } from 'nativewind';
import * as Haptics from 'expo-haptics';

interface NavigationGridProps {
  selectedProfileId: string | null;
  onNavigate?: (route: string) => void;
}

interface NavigationCard {
  id: string;
  title: string;
  subtitle: string;
  icon: string;
  iconLibrary: 'feather' | 'material';
  route: string;
  color: string;
  requiresProfile: boolean;
}

const navigationCards: NavigationCard[] = [
  {
    id: 'profiles',
    title: 'All Profiles',
    subtitle: 'Manage gift profiles',
    icon: 'account-multiple-outline',
    iconLibrary: 'material',
    route: '/profiles',
    color: '#A3002B',
    requiresProfile: false,
  },
  {
    id: 'profile-details',
    title: 'Profile Details',
    subtitle: 'View current profile',
    icon: 'user',
    iconLibrary: 'feather',
    route: '/profiles/[profileId]',
    color: '#E5355F',
    requiresProfile: true,
  },
  {
    id: 'notes',
    title: 'Notes',
    subtitle: 'Personal insights',
    icon: 'file-text',
    iconLibrary: 'feather',
    route: '/profiles/[profileId]/edit?focusSection=generalNotes',
    color: '#E87900',
    requiresProfile: true,
  },
  {
    id: 'dates',
    title: 'Important Dates',
    subtitle: 'Special occasions',
    icon: 'calendar',
    iconLibrary: 'feather',
    route: '/profiles/[profileId]/edit?focusSection=customDates',
    color: '#16A34A',
    requiresProfile: true,
  },
  {
    id: 'past-gifts',
    title: 'Past Gifts',
    subtitle: 'Gift history',
    icon: 'archive',
    iconLibrary: 'feather',
    route: '/profiles/[profileId]/edit?focusSection=pastGifts',
    color: '#7C3AED',
    requiresProfile: true,
  },
  {
    id: 'feedback',
    title: 'Liked & Disliked',
    subtitle: 'Gift preferences',
    icon: 'thumb-up-down-outline',
    iconLibrary: 'material',
    route: '/profiles/[profileId]/feedback',
    color: '#DC2626',
    requiresProfile: true,
  },
];

const NavigationGrid: React.FC<NavigationGridProps> = ({ 
  selectedProfileId, 
  onNavigate 
}) => {
  const router = useRouter();
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';
  const screenWidth = Dimensions.get('window').width;
  const cardWidth = (screenWidth - 60) / 2; // Account for padding and gap

  const handleCardPress = (card: NavigationCard) => {
    if (card.requiresProfile && !selectedProfileId) {
      // Could show a toast or alert here
      return;
    }

    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    
    let route = card.route;
    if (card.requiresProfile && selectedProfileId) {
      route = route.replace('[profileId]', selectedProfileId);
    }

    if (onNavigate) {
      onNavigate(route);
    } else {
      router.push(route as any);
    }
  };

  const renderIcon = (card: NavigationCard) => {
    const iconProps = {
      size: 24,
      color: card.color,
    };

    if (card.iconLibrary === 'material') {
      return <MaterialCommunityIcons name={card.icon as any} {...iconProps} />;
    }
    return <Feather name={card.icon as any} {...iconProps} />;
  };

  return (
    <View className="mb-6">
      <Text className="mb-4 text-lg font-semibold text-text-primary dark:text-text-primary-dark">
        Quick Actions
      </Text>
      
      <View className="flex-row flex-wrap justify-between">
        {navigationCards.map((card, index) => {
          const isDisabled = card.requiresProfile && !selectedProfileId;
          
          return (
            <Animated.View
              key={card.id}
              entering={SlideInUp.delay(index * 100).duration(400)}
              style={{ width: cardWidth }}
              className="mb-4"
            >
              <TouchableOpacity
                onPress={() => handleCardPress(card)}
                disabled={isDisabled}
                className={`
                  p-4 rounded-xl border shadow-sm
                  ${isDisabled 
                    ? 'bg-gray-100 dark:bg-gray-800 border-gray-200 dark:border-gray-700 opacity-50' 
                    : 'bg-card dark:bg-card-dark border-border dark:border-border-dark'
                  }
                  active:scale-95
                `}
                activeOpacity={0.7}
              >
                <View className="items-center">
                  <View 
                    className="p-3 rounded-full mb-3"
                    style={{ 
                      backgroundColor: isDisabled ? '#9CA3AF' : `${card.color}20` 
                    }}
                  >
                    {renderIcon(card)}
                  </View>
                  
                  <Text 
                    className={`
                      text-sm font-semibold text-center mb-1
                      ${isDisabled 
                        ? 'text-gray-400 dark:text-gray-600' 
                        : 'text-text-primary dark:text-text-primary-dark'
                      }
                    `}
                    numberOfLines={2}
                  >
                    {card.title}
                  </Text>
                  
                  <Text 
                    className={`
                      text-xs text-center
                      ${isDisabled 
                        ? 'text-gray-400 dark:text-gray-600' 
                        : 'text-text-secondary dark:text-text-secondary-dark'
                      }
                    `}
                    numberOfLines={2}
                  >
                    {card.subtitle}
                  </Text>
                </View>
              </TouchableOpacity>
            </Animated.View>
          );
        })}
      </View>
    </View>
  );
};

export default NavigationGrid;
