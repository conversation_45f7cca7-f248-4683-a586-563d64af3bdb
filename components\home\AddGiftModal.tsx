import React, { useState } from 'react';
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  Alert,
  Platform,
  ScrollView,
} from 'react-native';
import { Feather } from '@expo/vector-icons';
import DateTimePicker from '@react-native-community/datetimepicker';
import { useColorScheme } from 'nativewind';
import Animated, { FadeIn, SlideInDown } from 'react-native-reanimated';

import Input from '../ui/Input';
import Button from '../ui/Button';

interface AddGiftModalProps {
  isVisible: boolean;
  onClose: () => void;
  onSave: (giftData: {
    name: string;
    description?: string;
    occasion?: string;
    cost?: string;
    dateGiven: Date;
  }) => Promise<void>;
}

const AddGiftModal: React.FC<AddGiftModalProps> = ({
  isVisible,
  onClose,
  onSave,
}) => {
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [occasion, setOccasion] = useState('');
  const [cost, setCost] = useState('');
  const [dateGiven, setDateGiven] = useState(new Date());
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [saving, setSaving] = useState(false);

  const handleSave = async () => {
    if (!name.trim()) {
      Alert.alert('Error', 'Please enter a name for the gift.');
      return;
    }

    try {
      setSaving(true);
      await onSave({
        name: name.trim(),
        description: description.trim() || undefined,
        occasion: occasion.trim() || undefined,
        cost: cost.trim() || undefined,
        dateGiven,
      });
      
      // Reset form
      setName('');
      setDescription('');
      setOccasion('');
      setCost('');
      setDateGiven(new Date());
      onClose();
    } catch (error) {
      Alert.alert('Error', 'Failed to save gift. Please try again.');
    } finally {
      setSaving(false);
    }
  };

  const handleClose = () => {
    setName('');
    setDescription('');
    setOccasion('');
    setCost('');
    setDateGiven(new Date());
    onClose();
  };

  const onDateChange = (event: any, selectedDate?: Date) => {
    setShowDatePicker(Platform.OS === 'ios');
    if (selectedDate) {
      setDateGiven(selectedDate);
    }
  };

  return (
    <Modal
      visible={isVisible}
      transparent
      animationType="fade"
      onRequestClose={handleClose}
    >
      <View className="flex-1 bg-black/50 justify-center items-center p-4">
        <Animated.View
          entering={SlideInDown.duration(300)}
          className="w-full max-w-sm bg-card dark:bg-card-dark rounded-xl shadow-lg max-h-[80%]"
        >
          {/* Header */}
          <View className="flex-row items-center justify-between p-4 border-b border-border dark:border-border-dark">
            <Text className="text-lg font-semibold text-text-primary dark:text-text-primary-dark">
              Add Past Gift
            </Text>
            <TouchableOpacity
              onPress={handleClose}
              className="p-2 rounded-full active:bg-gray-100 dark:active:bg-gray-800"
            >
              <Feather name="x" size={20} color={isDark ? '#FFFFFF' : '#000000'} />
            </TouchableOpacity>
          </View>

          {/* Content */}
          <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
            <View className="p-4 space-y-4">
              <View>
                <Text className="text-sm font-medium text-text-primary dark:text-text-primary-dark mb-2">
                  Gift Name *
                </Text>
                <Input
                  value={name}
                  onChangeText={setName}
                  placeholder="e.g., Necklace, Book, Concert Tickets"
                  className="mb-4"
                />
              </View>

              <View>
                <Text className="text-sm font-medium text-text-primary dark:text-text-primary-dark mb-2">
                  Description (Optional)
                </Text>
                <Input
                  value={description}
                  onChangeText={setDescription}
                  placeholder="Brief description of the gift"
                  multiline
                  numberOfLines={3}
                  className="mb-4"
                />
              </View>

              <View>
                <Text className="text-sm font-medium text-text-primary dark:text-text-primary-dark mb-2">
                  Occasion (Optional)
                </Text>
                <Input
                  value={occasion}
                  onChangeText={setOccasion}
                  placeholder="e.g., Birthday, Anniversary, Christmas"
                  className="mb-4"
                />
              </View>

              <View>
                <Text className="text-sm font-medium text-text-primary dark:text-text-primary-dark mb-2">
                  Cost (Optional)
                </Text>
                <Input
                  value={cost}
                  onChangeText={setCost}
                  placeholder="e.g., 50.00"
                  keyboardType="decimal-pad"
                  className="mb-4"
                />
              </View>

              <View>
                <Text className="text-sm font-medium text-text-primary dark:text-text-primary-dark mb-2">
                  Date Given
                </Text>
                <TouchableOpacity
                  onPress={() => setShowDatePicker(true)}
                  className="flex-row items-center justify-between p-3 border border-border dark:border-border-dark rounded-lg bg-background dark:bg-background-dark"
                >
                  <Text className="text-text-primary dark:text-text-primary-dark">
                    {dateGiven.toLocaleDateString()}
                  </Text>
                  <Feather name="calendar" size={20} color={isDark ? '#FFFFFF' : '#000000'} />
                </TouchableOpacity>
              </View>

              {showDatePicker && (
                <DateTimePicker
                  value={dateGiven}
                  mode="date"
                  display="default"
                  onChange={onDateChange}
                />
              )}
            </View>
          </ScrollView>

          {/* Footer */}
          <View className="flex-row space-x-3 p-4 border-t border-border dark:border-border-dark">
            <Button
              title="Cancel"
              onPress={handleClose}
              variant="secondary"
              className="flex-1"
              disabled={saving}
            />
            <Button
              title="Save"
              onPress={handleSave}
              variant="primary"
              className="flex-1"
              isLoading={saving}
              disabled={saving}
            />
          </View>
        </Animated.View>
      </View>
    </Modal>
  );
};

export default AddGiftModal;
