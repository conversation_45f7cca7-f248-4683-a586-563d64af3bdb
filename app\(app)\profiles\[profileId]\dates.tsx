import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  SafeAreaView,
  FlatList,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { Feather } from '@expo/vector-icons';
import Animated, { FadeIn, SlideInRight } from 'react-native-reanimated';
import { useColorScheme } from 'nativewind';
import { format } from 'date-fns';

import { useAuth } from '../../../../contexts/AuthContext';
import { 
  getSignificantOtherById, 
  updateSignificantOther 
} from '../../../../services/profileService';
import { SignificantOtherProfile, CustomDate } from '../../../../functions/src/types/firestore';
import LoadingIndicator from '../../../../components/ui/LoadingIndicator';
import Card from '../../../../components/ui/Card';
import Button from '../../../../components/ui/Button';

const DatesScreen = () => {
  const { profileId } = useLocalSearchParams();
  const router = useRouter();
  const { user } = useAuth();
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  const [profile, setProfile] = useState<SignificantOtherProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [deletingIds, setDeletingIds] = useState<Set<string>>(new Set());

  const id = Array.isArray(profileId) ? profileId[0] : profileId;

  const fetchData = useCallback(async () => {
    if (!user?.uid || !id) return;

    try {
      setLoading(true);
      setError(null);

      const profileData = await getSignificantOtherById(user.uid, id);
      setProfile(profileData);
    } catch (err) {
      setError('Failed to load dates. Please try again.');
      console.error('Error fetching dates:', err);
    } finally {
      setLoading(false);
    }
  }, [user?.uid, id]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  const handleDeleteDate = async (dateIndex: number) => {
    if (!profile || !user?.uid) return;

    const customDate = profile.customDates?.[dateIndex];
    if (!customDate) return;

    Alert.alert(
      'Delete Date',
      `Are you sure you want to delete "${customDate.name}"?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            const dateId = customDate.id || `temp-${dateIndex}`;
            setDeletingIds(prev => new Set(prev).add(dateId));
            
            try {
              const updatedDates = profile.customDates?.filter((_, index) => index !== dateIndex) || [];
              await updateSignificantOther(user.uid, id, {
                customDates: updatedDates,
              });
              
              setProfile(prev => prev ? { ...prev, customDates: updatedDates } : null);
            } catch (err) {
              Alert.alert('Error', 'Failed to delete date. Please try again.');
            } finally {
              setDeletingIds(prev => {
                const newSet = new Set(prev);
                newSet.delete(dateId);
                return newSet;
              });
            }
          },
        },
      ]
    );
  };

  const renderDateItem = ({ item, index }: { item: CustomDate; index: number }) => {
    const dateId = item.id || `temp-${index}`;
    const isDeleting = deletingIds.has(dateId);

    return (
      <Animated.View
        entering={SlideInRight.delay(index * 100).duration(400)}
        className="mb-4"
      >
        <Card className="p-4">
          <View className="flex-row items-start justify-between">
            <View className="flex-1 mr-3">
              <View className="flex-row items-center mb-2">
                <View className="p-2 rounded-full mr-3" style={{ backgroundColor: '#A3002B20' }}>
                  <Feather name="calendar" size={18} color="#A3002B" />
                </View>
                <View className="flex-1">
                  <Text className="text-base font-semibold text-text-primary dark:text-text-primary-dark">
                    {item.name}
                  </Text>
                  {item.type && (
                    <Text className="text-sm text-text-secondary dark:text-text-secondary-dark">
                      {item.type}
                    </Text>
                  )}
                </View>
              </View>
              
              {item.date && (
                <Text className="text-sm text-text-secondary dark:text-text-secondary-dark ml-12">
                  {format(item.date.toDate(), 'MMMM d, yyyy')}
                </Text>
              )}
            </View>

            <TouchableOpacity
              onPress={() => handleDeleteDate(index)}
              disabled={isDeleting}
              className="p-2 rounded-full active:bg-error/10"
            >
              {isDeleting ? (
                <LoadingIndicator size="small" color="#DC2626" />
              ) : (
                <Feather name="trash-2" size={18} color="#DC2626" />
              )}
            </TouchableOpacity>
          </View>
        </Card>
      </Animated.View>
    );
  };

  if (loading) {
    return (
      <SafeAreaView className="flex-1 bg-background dark:bg-background-dark">
        <View className="flex-1 items-center justify-center">
          <LoadingIndicator size="large" color="#A3002B" />
          <Text className="mt-4 text-base text-text-secondary dark:text-text-secondary-dark">
            Loading dates...
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  if (error) {
    return (
      <SafeAreaView className="flex-1 bg-background dark:bg-background-dark">
        <View className="flex-1 items-center justify-center px-6">
          <Feather name="alert-circle" size={48} color="#DC2626" />
          <Text className="mt-4 text-lg font-semibold text-center text-error dark:text-error-dark">
            {error}
          </Text>
          <Button
            title="Try Again"
            onPress={fetchData}
            variant="primary"
            className="mt-4"
          />
        </View>
      </SafeAreaView>
    );
  }

  const dates = profile?.customDates || [];
  const basicDates = [];
  
  // Add birthday and anniversary to the list if they exist
  if (profile?.birthday) {
    basicDates.push({
      id: 'birthday',
      name: 'Birthday',
      type: 'Birthday',
      date: profile.birthday,
    });
  }
  
  if (profile?.anniversary) {
    basicDates.push({
      id: 'anniversary',
      name: 'Anniversary',
      type: 'Anniversary',
      date: profile.anniversary,
    });
  }

  const allDates = [...basicDates, ...dates];

  return (
    <SafeAreaView className="flex-1 bg-background dark:bg-background-dark">
      <View className="flex-1 px-4 py-6">
        {profile && (
          <View className="mb-6">
            <Text className="text-2xl font-bold text-text-primary dark:text-text-primary-dark">
              {profile.name}'s Important Dates
            </Text>
            <Text className="text-base text-text-secondary dark:text-text-secondary-dark">
              Special occasions and memorable dates
            </Text>
          </View>
        )}

        {allDates.length === 0 ? (
          <Animated.View
            entering={FadeIn.duration(600)}
            className="flex-1 items-center justify-center px-6"
          >
            <View className="items-center justify-center w-20 h-20 mb-6 rounded-full bg-primary/10 dark:bg-primary-dark/10">
              <Feather
                name="calendar"
                size={32}
                color={isDark ? '#C70039' : '#A3002B'}
              />
            </View>
            <Text className="text-lg font-semibold text-center text-text-primary dark:text-text-primary-dark mb-2">
              No Important Dates Yet
            </Text>
            <Text className="text-base text-center text-text-secondary dark:text-text-secondary-dark mb-6">
              Add important dates to remember special occasions for {profile?.name}
            </Text>
            <Button
              title="Add First Date"
              onPress={() => router.push(`/profiles/${id}/edit?focusSection=customDates`)}
              variant="primary"
            />
          </Animated.View>
        ) : (
          <FlatList
            data={allDates}
            renderItem={({ item, index }) => {
              // Don't allow deletion of basic dates (birthday/anniversary)
              if (item.id === 'birthday' || item.id === 'anniversary') {
                return (
                  <Animated.View
                    entering={SlideInRight.delay(index * 100).duration(400)}
                    className="mb-4"
                  >
                    <Card className="p-4">
                      <View className="flex-row items-center">
                        <View className="p-2 rounded-full mr-3" style={{ backgroundColor: '#A3002B20' }}>
                          <Feather name="calendar" size={18} color="#A3002B" />
                        </View>
                        <View className="flex-1">
                          <Text className="text-base font-semibold text-text-primary dark:text-text-primary-dark">
                            {item.name}
                          </Text>
                          <Text className="text-sm text-text-secondary dark:text-text-secondary-dark">
                            {format(item.date.toDate(), 'MMMM d, yyyy')}
                          </Text>
                        </View>
                      </View>
                    </Card>
                  </Animated.View>
                );
              }
              
              // Custom dates can be deleted
              const customDateIndex = index - basicDates.length;
              return renderDateItem({ item, index: customDateIndex });
            }}
            keyExtractor={(item, index) => item.id || `date-${index}`}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={{ paddingBottom: 20 }}
          />
        )}
      </View>
    </SafeAreaView>
  );
};

export default DatesScreen;
