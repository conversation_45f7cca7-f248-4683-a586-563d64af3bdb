import React from 'react';
import { Timestamp } from 'firebase/firestore';
import { View, Text, TouchableOpacity } from 'react-native';
import { Feather } from '@expo/vector-icons'; // Assuming Feather icons from Expo
import { CustomDate } from '../../types/firestore'; // Use client-side types

// Helper function (placeholder - assuming it exists elsewhere or needs to be created)
const formatDate = (date: Date | Timestamp | null): string => {
  if (!date) {
    return "No Date"; // Or a suitable placeholder
  }
  let jsDate: Date;
  if (date instanceof Date) {
    jsDate = date;
  } else {
    // Assuming date is a Firestore Timestamp
    jsDate = date.toDate();
  }
  // Implement date formatting logic here or import from a utility file
  return jsDate.toDateString(); // Placeholder implementation
};

interface CustomDateFormInputProps {
  customDate: CustomDate;
  onRemove: () => void;
  index: number;
}

const CustomDateFormInput: React.FC<CustomDateFormInputProps> = ({ customDate, onRemove, index }) => {
  return (
    <View className="flex-row items-center justify-between p-2 border-b border-gray-200">
      <View>
        <Text className="text-base font-semibold">{customDate.name}</Text>
        <Text className="text-sm text-gray-500">{formatDate(customDate.date)}</Text>
      </View>
      <TouchableOpacity onPress={onRemove} accessibilityLabel="Remove custom date">
        <Feather name="trash-2" size={24} color="red" />
      </TouchableOpacity>
    </View>
  );
};

export default CustomDateFormInput;