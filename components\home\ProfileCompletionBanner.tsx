import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { Feather } from '@expo/vector-icons';
import Animated, { FadeIn } from 'react-native-reanimated';
import { useColorScheme } from 'nativewind';
import { useRouter } from 'expo-router';

import { SignificantOtherProfile } from '../../functions/src/types/firestore';

interface ProfileCompletionBannerProps {
  profile: SignificantOtherProfile | null;
  className?: string;
}

const calculateProfileCompletion = (profile: SignificantOtherProfile | null): number => {
  if (!profile) return 0;
  
  let completedFields = 0;
  const totalFields = 10; // Total number of fields we're checking
  
  // Basic info (3 fields)
  if (profile.name?.trim()) completedFields++;
  if (profile.relationship?.trim()) completedFields++;
  if (profile.birthday) completedFields++;
  
  // Interests and preferences (3 fields)
  if (profile.interests?.length > 0) completedFields++;
  if (profile.dislikes?.length > 0) completedFields++;
  if (profile.preferences?.favoriteColor || profile.preferences?.preferredStyle) completedFields++;
  
  // Additional data (4 fields)
  if (profile.anniversary) completedFields++;
  if (profile.generalNotes?.length > 0) completedFields++;
  if (profile.customDates?.length > 0) completedFields++;
  if (profile.pastGiftsGiven?.length > 0) completedFields++;
  
  return Math.round((completedFields / totalFields) * 100);
};

const ProfileCompletionBanner: React.FC<ProfileCompletionBannerProps> = ({ 
  profile, 
  className = '' 
}) => {
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';
  const router = useRouter();
  
  const completionPercentage = calculateProfileCompletion(profile);
  
  // Only show banner if completion is below 50%
  if (!profile || completionPercentage >= 50) {
    return null;
  }
  
  const handleCompleteProfile = () => {
    if (profile) {
      router.push(`/profiles/${profile.profileId}/edit`);
    }
  };
  
  return (
    <Animated.View
      entering={FadeIn.duration(600)}
      className={`mb-6 ${className}`}
    >
      <View className="p-4 rounded-xl bg-warning/10 dark:bg-warning-dark/10 border border-warning/30 dark:border-warning-dark/30">
        <View className="flex-row items-start">
          <View className="p-2 rounded-full mr-3 bg-warning/20 dark:bg-warning-dark/20">
            <Feather 
              name="alert-triangle" 
              size={20} 
              color={isDark ? '#F59E0B' : '#D97706'} 
            />
          </View>
          
          <View className="flex-1">
            <Text className="text-base font-semibold text-warning dark:text-warning-dark mb-1">
              Profile Incomplete ({completionPercentage}%)
            </Text>
            
            <Text className="text-sm text-text-secondary dark:text-text-secondary-dark mb-3 leading-relaxed">
              Complete {profile.name}'s profile to get better gift recommendations. 
              Add more details like interests, preferences, and important dates.
            </Text>
            
            {/* Progress Bar */}
            <View className="mb-3">
              <View className="h-2 rounded-full bg-gray-200 dark:bg-gray-700 overflow-hidden">
                <View 
                  className="h-full rounded-full bg-warning dark:bg-warning-dark"
                  style={{ width: `${completionPercentage}%` }}
                />
              </View>
              <Text className="text-xs text-text-secondary dark:text-text-secondary-dark mt-1">
                {completionPercentage}% complete
              </Text>
            </View>
            
            <TouchableOpacity
              onPress={handleCompleteProfile}
              className="flex-row items-center justify-center py-2 px-4 rounded-lg bg-warning dark:bg-warning-dark active:opacity-80"
            >
              <Feather 
                name="edit-3" 
                size={16} 
                color="white" 
                style={{ marginRight: 8 }}
              />
              <Text className="text-sm font-medium text-white">
                Complete Profile
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Animated.View>
  );
};

export default ProfileCompletionBanner;
