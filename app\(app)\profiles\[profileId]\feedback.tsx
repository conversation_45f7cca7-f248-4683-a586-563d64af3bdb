import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  SafeAreaView,
  FlatList,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { Feather, MaterialCommunityIcons } from '@expo/vector-icons';
import Animated, { FadeIn, SlideInRight } from 'react-native-reanimated';
import { useColorScheme } from 'nativewind';

import { useAuth } from '../../../../contexts/AuthContext';
import {
  getProfileFeedback,
  deleteRecommendationFeedback,
  type FeedbackEntry,
} from '../../../../services/recommendationService';
import { getSignificantOtherById } from '../../../../services/profileService';
import { SignificantOtherProfile } from '../../../../functions/src/types/firestore';
import LoadingIndicator from '../../../../components/ui/LoadingIndicator';
import Card from '../../../../components/ui/Card';
import Button from '../../../../components/ui/Button';

const FeedbackScreen = () => {
  const { profileId } = useLocalSearchParams();
  const router = useRouter();
  const { user } = useAuth();
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  const [profile, setProfile] = useState<SignificantOtherProfile | null>(null);
  const [feedback, setFeedback] = useState<FeedbackEntry[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [deletingIds, setDeletingIds] = useState<Set<string>>(new Set());

  const id = Array.isArray(profileId) ? profileId[0] : profileId;

  const fetchData = useCallback(async () => {
    if (!user?.uid || !id) return;

    try {
      setLoading(true);
      setError(null);

      const [profileData, feedbackData] = await Promise.all([
        getSignificantOtherById(user.uid, id),
        getProfileFeedback(id),
      ]);

      setProfile(profileData);
      setFeedback(feedbackData);
    } catch (err) {
      setError('Failed to load data. Please try again.');
      console.error('Error fetching feedback data:', err);
    } finally {
      setLoading(false);
    }
  }, [user?.uid, id]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  const handleDeleteFeedback = async (feedbackEntry: FeedbackEntry) => {
    if (!feedbackEntry.id) return;

    Alert.alert(
      'Remove Feedback',
      `Are you sure you want to remove this ${feedbackEntry.feedbackType}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Remove',
          style: 'destructive',
          onPress: async () => {
            setDeletingIds(prev => new Set(prev).add(feedbackEntry.id!));
            try {
              await deleteRecommendationFeedback(feedbackEntry.id!);
              setFeedback(prev => prev.filter(f => f.id !== feedbackEntry.id));
            } catch (err) {
              Alert.alert('Error', 'Failed to remove feedback. Please try again.');
            } finally {
              setDeletingIds(prev => {
                const newSet = new Set(prev);
                newSet.delete(feedbackEntry.id!);
                return newSet;
              });
            }
          },
        },
      ]
    );
  };

  const renderFeedbackItem = ({ item, index }: { item: FeedbackEntry; index: number }) => {
    const isLike = item.feedbackType === 'like';
    const isDeleting = item.id && deletingIds.has(item.id);

    return (
      <Animated.View
        entering={SlideInRight.delay(index * 100).duration(400)}
        className="mb-4"
      >
        <Card className="p-4">
          <View className="flex-row items-start justify-between">
            <View className="flex-1 mr-3">
              <View className="flex-row items-center mb-2">
                <MaterialCommunityIcons
                  name={isLike ? 'thumb-up' : 'thumb-down'}
                  size={20}
                  color={isLike ? '#16A34A' : '#DC2626'}
                />
                <Text className="ml-2 text-sm font-medium text-text-secondary dark:text-text-secondary-dark">
                  {isLike ? 'Liked' : 'Disliked'}
                </Text>
              </View>
              
              <Text className="text-base font-semibold text-text-primary dark:text-text-primary-dark mb-1">
                {item.recommendationDetails?.name || 'Unknown Gift'}
              </Text>
              
              {item.recommendationDetails?.description && (
                <Text className="text-sm text-text-secondary dark:text-text-secondary-dark mb-2">
                  {item.recommendationDetails.description}
                </Text>
              )}
              
              <Text className="text-xs text-text-secondary dark:text-text-secondary-dark">
                {item.timestamp instanceof Date 
                  ? item.timestamp.toLocaleDateString()
                  : new Date(item.timestamp).toLocaleDateString()
                }
              </Text>
            </View>

            <TouchableOpacity
              onPress={() => handleDeleteFeedback(item)}
              disabled={isDeleting}
              className="p-2 rounded-full active:bg-error/10"
            >
              {isDeleting ? (
                <LoadingIndicator size="small" color="#DC2626" />
              ) : (
                <Feather name="trash-2" size={18} color="#DC2626" />
              )}
            </TouchableOpacity>
          </View>
        </Card>
      </Animated.View>
    );
  };

  const likedItems = feedback.filter(f => f.feedbackType === 'like');
  const dislikedItems = feedback.filter(f => f.feedbackType === 'dislike');

  if (loading) {
    return (
      <SafeAreaView className="flex-1 bg-background dark:bg-background-dark">
        <View className="flex-1 items-center justify-center">
          <LoadingIndicator size="large" color="#A3002B" />
          <Text className="mt-4 text-base text-text-secondary dark:text-text-secondary-dark">
            Loading preferences...
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  if (error) {
    return (
      <SafeAreaView className="flex-1 bg-background dark:bg-background-dark">
        <View className="flex-1 items-center justify-center px-6">
          <Feather name="alert-circle" size={48} color="#DC2626" />
          <Text className="mt-4 text-lg font-semibold text-center text-error dark:text-error-dark">
            {error}
          </Text>
          <Button
            title="Try Again"
            onPress={fetchData}
            variant="primary"
            className="mt-4"
          />
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView className="flex-1 bg-background dark:bg-background-dark">
      <View className="flex-1 px-4 py-6">
        {profile && (
          <View className="mb-6">
            <Text className="text-2xl font-bold text-text-primary dark:text-text-primary-dark">
              {profile.name}'s Preferences
            </Text>
            <Text className="text-base text-text-secondary dark:text-text-secondary-dark">
              Gift recommendations you've liked and disliked
            </Text>
          </View>
        )}

        {feedback.length === 0 ? (
          <Animated.View
            entering={FadeIn.duration(600)}
            className="flex-1 items-center justify-center px-6"
          >
            <View className="items-center justify-center w-20 h-20 mb-6 rounded-full bg-primary/10 dark:bg-primary-dark/10">
              <MaterialCommunityIcons
                name="thumb-up-down-outline"
                size={32}
                color={isDark ? '#C70039' : '#A3002B'}
              />
            </View>
            <Text className="text-lg font-semibold text-center text-text-primary dark:text-text-primary-dark mb-2">
              No Preferences Yet
            </Text>
            <Text className="text-base text-center text-text-secondary dark:text-text-secondary-dark">
              Start rating gift recommendations to build a preference history
            </Text>
          </Animated.View>
        ) : (
          <FlatList
            data={[
              ...(likedItems.length > 0 ? [{ type: 'header', title: 'Liked Gifts', data: likedItems }] : []),
              ...likedItems.map(item => ({ type: 'item', data: item })),
              ...(dislikedItems.length > 0 ? [{ type: 'header', title: 'Disliked Gifts', data: dislikedItems }] : []),
              ...dislikedItems.map(item => ({ type: 'item', data: item })),
            ]}
            renderItem={({ item, index }) => {
              if (item.type === 'header') {
                return (
                  <View className="mb-4 mt-2">
                    <Text className="text-lg font-semibold text-text-primary dark:text-text-primary-dark">
                      {item.title} ({item.data.length})
                    </Text>
                  </View>
                );
              }
              return renderFeedbackItem({ item: item.data, index });
            }}
            keyExtractor={(item, index) => 
              item.type === 'header' ? `header-${item.title}` : `item-${item.data.id || index}`
            }
            showsVerticalScrollIndicator={false}
            contentContainerStyle={{ paddingBottom: 20 }}
          />
        )}
      </View>
    </SafeAreaView>
  );
};

export default FeedbackScreen;
