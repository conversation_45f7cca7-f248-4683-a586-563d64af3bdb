import React, { useState, useEffect } from 'react';
import { View, Text } from 'react-native';
import Animated, { FadeIn, FadeOut } from 'react-native-reanimated';
import { useColorScheme } from 'nativewind';

interface MotivationalHeaderProps {
  userName?: string;
}

const motivationalMessages = [
  "Making your loved ones happy starts with small surprises",
  "Every gift tells a story",
  "The best gifts come from the heart",
  "Thoughtful giving strengthens relationships",
  "A perfect gift shows how much you care",
  "Great gifts create lasting memories",
  "The joy of giving is the greatest gift of all",
  "Small gestures, big impact",
  "Love is in the details",
  "Meaningful gifts speak louder than words"
];

const MotivationalHeader: React.FC<MotivationalHeaderProps> = ({ userName }) => {
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';
  const [currentMessageIndex, setCurrentMessageIndex] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentMessageIndex((prev) => (prev + 1) % motivationalMessages.length);
    }, 5000); // Change message every 5 seconds

    return () => clearInterval(interval);
  }, []);

  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return 'Good morning';
    if (hour < 17) return 'Good afternoon';
    return 'Good evening';
  };

  return (
    <View className="mb-6">
      {/* Greeting */}
      <Animated.View
        entering={FadeIn.duration(600)}
        className="mb-3"
      >
        <Text className="text-lg font-medium text-text-primary dark:text-text-primary-dark">
          {getGreeting()}{userName ? `, ${userName}` : ''}!
        </Text>
      </Animated.View>

      {/* Motivational Message */}
      <Animated.View
        key={currentMessageIndex}
        entering={FadeIn.duration(800)}
        exiting={FadeOut.duration(400)}
        className="p-4 rounded-xl bg-primary/5 dark:bg-primary-dark/5 border-l-4 border-primary dark:border-primary-dark"
      >
        <Text className="text-base text-text-secondary dark:text-text-secondary-dark italic leading-relaxed">
          "{motivationalMessages[currentMessageIndex]}"
        </Text>
      </Animated.View>
    </View>
  );
};

export default MotivationalHeader;
