import React from 'react';
import { TouchableOpacity, Text, Platform } from 'react-native'; // Added Text, Platform
import { Stack, Link, useLocalSearchParams } from 'expo-router';
import { Feather } from '@expo/vector-icons'; // Import Feather icons
// No need to import tailwindConfig here directly for color values
// We'll use NativeWind classes which an Expo Router project should have configured

export default function ProfilesLayout() {
  // Define consistent styling for header action text/icons
  // These classes will pull from your new Pomegranate theme via NativeWind
  const headerActionStyleLight = "text-primary-500"; // Uses #A3002B
  const headerActionStyleDark = "dark:text-primary-dark"; // Uses #C70039
  const headerActionClass = `${headerActionStyleLight} ${headerActionStyleDark}`;

  return (
    <Stack
      screenOptions={{
        // Apply global header styles if needed, e.g., headerTintColor, headerStyle
        // headerTintColor: '#3D1C26', // Example: text-primary from Pomegranate for titles
        // headerStyle: { backgroundColor: '#FFFBF7' }, // Example: background from Pomegranate
        // For dark mode, these would need to be dynamic or use a theme provider context
      }}
    >
      <Stack.Screen
        name="index"
        options={{
          title: 'Profiles',
          headerRight: () => (
            <Link href="/profiles/add" asChild>
              <TouchableOpacity
                className="p-2 mr-1" // Added padding for touch target, mr-1 for slight offset from edge
                accessibilityRole="button"
                accessibilityLabel="Add new profile"
              >
                {/* For Pomegranate theme, primary.DEFAULT (#A3002B) or accent.DEFAULT (#E5355F) */}
                <Feather
                  name="plus-circle" // Using a slightly more substantial 'plus' icon
                  size={25}
                  className={headerActionClass}
                />
              </TouchableOpacity>
            </Link>
          ),
        }}
      />
      <Stack.Screen
        name="[profileId]/index"
        options={{
          title: 'Profile Detail', // Title can be made dynamic later if needed
          headerRight: () => {
            const { profileId } = useLocalSearchParams();
            const id = Array.isArray(profileId) ? profileId[0] : profileId;
            return (
              <Link href={{ pathname: "/profiles/[profileId]/edit", params: { profileId: id } }} asChild>
                <TouchableOpacity
                  className="px-3 py-2 mr-1" // Adjusted padding for text button
                  accessibilityRole="button"
                  accessibilityLabel="Edit profile"
                >
                  <Text className={`text-base font-medium ${headerActionClass}`}>
                    Edit
                  </Text>
                </TouchableOpacity>
              </Link>
            );
          },
        }}
      />
      <Stack.Screen
        name="add"
        options={{
          title: 'Add Profile',
          // presentation: 'modal', // Consider 'modal' for add/edit screens
        }}
      />
      <Stack.Screen
        name="[profileId]/edit"
        options={{
          title: 'Edit Profile',
          // presentation: 'modal',
        }}
      />
    </Stack>
  );
}