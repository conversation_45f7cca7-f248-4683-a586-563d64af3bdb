// utils/dateUtils.ts
import { Timestamp } from 'firebase/firestore';

export const formatDateForDisplay = (dateInput: Date | Timestamp | null | undefined): string => {
  if (!dateInput) {
    return "Select Date";
  }
  const dateObject = (dateInput instanceof Timestamp) ? dateInput.toDate() : dateInput;
  return dateObject.toLocaleDateString(undefined, {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });
};