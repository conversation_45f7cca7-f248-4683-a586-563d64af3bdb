import React from 'react';
import { View, Text } from 'react-native';
import { Feather } from '@expo/vector-icons';
import Animated, { FadeIn } from 'react-native-reanimated';
import { useColorScheme } from 'nativewind';
import { format } from 'date-fns';

import { SignificantOtherProfile } from '../../functions/src/types/firestore';

interface KeyDatesDisplayProps {
  profile: SignificantOtherProfile | null;
  className?: string;
}

const KeyDatesDisplay: React.FC<KeyDatesDisplayProps> = ({
  profile,
  className = ''
}) => {
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  if (!profile) return null;

  const hasBirthday = profile.birthday;
  const hasAnniversary = profile.anniversary;

  // Don't show if no key dates are set
  if (!hasBirthday && !hasAnniversary) return null;

  return (
    <Animated.View
      entering={FadeIn.duration(600)}
      className={`mb-6 ${className}`}
    >
      <Text className="mb-3 text-lg font-semibold text-text-primary dark:text-text-primary-dark">
        Key Dates
      </Text>

      <View className="space-y-3">
        {hasBirthday && (
          <View className="flex-row items-center p-3 border rounded-lg bg-card dark:bg-card-dark border-border dark:border-border-dark">
            <View className="p-2 mr-3 rounded-full bg-primary/10 dark:bg-primary-dark/10">
              <Feather
                name="gift"
                size={18}
                color={isDark ? '#C70039' : '#A3002B'}
              />
            </View>

            <View className="flex-1">
              <Text className="text-sm font-medium text-text-primary dark:text-text-primary-dark">
                Birthday
              </Text>
              <Text className="text-base text-text-secondary dark:text-text-secondary-dark">
                {profile.birthday && format(profile.birthday.toDate(), 'MMMM d, yyyy')}
              </Text>
            </View>

            <View className="items-end">
              <Text className="text-xs text-text-secondary dark:text-text-secondary-dark">
                {profile.birthday && format(profile.birthday.toDate(), 'MMM d')}
              </Text>
            </View>
          </View>
        )}

        {hasAnniversary && (
          <View className="flex-row items-center p-3 border rounded-lg bg-card dark:bg-card-dark border-border dark:border-border-dark">
            <View className="p-2 mr-3 rounded-full bg-accent/10 dark:bg-accent-dark/10">
              <Feather
                name="heart"
                size={18}
                color={isDark ? '#E87900' : '#D97706'}
              />
            </View>

            <View className="flex-1">
              <Text className="text-sm font-medium text-text-primary dark:text-text-primary-dark">
                Anniversary
              </Text>
              <Text className="text-base text-text-secondary dark:text-text-secondary-dark">
                {profile.anniversary && format(profile.anniversary.toDate(), 'MMMM d, yyyy')}
              </Text>
            </View>

            <View className="items-end">
              <Text className="text-xs text-text-secondary dark:text-text-secondary-dark">
                {profile.anniversary && format(profile.anniversary.toDate(), 'MMM d')}
              </Text>
            </View>
          </View>
        )}
      </View>
    </Animated.View>
  );
};

export default KeyDatesDisplay;
